{"name": "@types/node", "version": "18.19.112", "description": "TypeScript definitions for node", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/node", "license": "MIT", "contributors": [{"name": "Microsoft TypeScript", "githubUsername": "Microsoft", "url": "https://github.com/Microsoft"}, {"name": "<PERSON>", "githubUsername": "jkomyno", "url": "https://github.com/jkomyno"}, {"name": "Alvis <PERSON>", "githubUsername": "alvis", "url": "https://github.com/alvis"}, {"name": "<PERSON>", "githubUsername": "r3nya", "url": "https://github.com/r3nya"}, {"name": "<PERSON>", "githubUsername": "btoueg", "url": "https://github.com/btoueg"}, {"name": "Chigozirim C.", "githubUsername": "smac89", "url": "https://github.com/smac89"}, {"name": "<PERSON>", "githubUsername": "touffy", "url": "https://github.com/touffy"}, {"name": "<PERSON><PERSON><PERSON>", "githubUsername": "DeividasBakanas", "url": "https://github.com/DeividasBakanas"}, {"name": "<PERSON>", "githubUsername": "eyqs", "url": "https://github.com/eyqs"}, {"name": "<PERSON><PERSON>", "githubUsername": "Hannes-<PERSON><PERSON>-CK", "url": "https://github.com/<PERSON><PERSON>-<PERSON>-CK"}, {"name": "<PERSON><PERSON>", "githubUsername": "hoo29", "url": "https://github.com/hoo29"}, {"name": "<PERSON><PERSON>", "githubUsername": "kjin", "url": "https://github.com/kjin"}, {"name": "<PERSON>", "githubUsername": "a<PERSON><PERSON><PERSON>", "url": "https://github.com/ajafff"}, {"name": "Lishude", "githubUsername": "islishude", "url": "https://github.com/islishude"}, {"name": "<PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/mwiktorczyk"}, {"name": "<PERSON><PERSON><PERSON>", "githubUsername": "mohsen1", "url": "https://github.com/mohsen1"}, {"name": "<PERSON><PERSON>", "githubUsername": "galkin", "url": "https://github.com/galkin"}, {"name": "<PERSON><PERSON><PERSON>", "githubUsername": "parambirs", "url": "https://github.com/parambirs"}, {"name": "<PERSON>", "githubUsername": "eps1lon", "url": "https://github.com/eps1lon"}, {"name": "<PERSON>", "githubUsername": "Simon<PERSON><PERSON><PERSON>", "url": "https://github.com/SimonSchick"}, {"name": "<PERSON>", "githubUsername": "ThomasdenH", "url": "https://github.com/ThomasdenH"}, {"name": "<PERSON><PERSON><PERSON>", "githubUsername": "WilcoBakker", "url": "https://github.com/WilcoBakker"}, {"name": "wwwy3y3", "githubUsername": "wwwy3y3", "url": "https://github.com/wwwy3y3"}, {"name": "<PERSON>", "githubUsername": "samuela", "url": "https://github.com/samuela"}, {"name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/kuehlein"}, {"name": "<PERSON><PERSON>", "githubUsername": "b<PERSON>y", "url": "https://github.com/bhongy"}, {"name": "<PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/chyzwar"}, {"name": "<PERSON><PERSON><PERSON>", "githubUsername": "trivikr", "url": "https://github.com/trivikr"}, {"name": "<PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON>ny", "url": "https://github.com/yoursunny"}, {"name": "<PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/qwelias"}, {"name": "ExE Boss", "githubUsername": "ExE-Boss", "url": "https://github.com/ExE-Boss"}, {"name": "<PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/peter<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "githubUsername": "addaleax", "url": "https://github.com/addaleax"}, {"name": "<PERSON>", "githubUsername": "victor<PERSON>in", "url": "https://github.com/victorperin"}, {"name": "NodeJS Contributors", "githubUsername": "NodeJS", "url": "https://github.com/NodeJS"}, {"name": "<PERSON><PERSON>", "githubUsername": "LinusU", "url": "https://github.com/LinusU"}, {"name": "wafuwafu13", "githubUsername": "wafuwafu13", "url": "https://github.com/wafuwafu13"}, {"name": "<PERSON>", "githubUsername": "mcollina", "url": "https://github.com/mcollina"}, {"name": "<PERSON>", "githubUsername": "Semigradsky", "url": "https://github.com/Semigradsky"}], "main": "", "types": "index.d.ts", "typesVersions": {"<=5.6": {"*": ["ts5.6/*"]}}, "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/node"}, "scripts": {}, "dependencies": {"undici-types": "~5.26.4"}, "peerDependencies": {}, "typesPublisherContentHash": "3b0022e6fd68674bfe09bfec92263763b1fba74ce671da541765e24b2af05d76", "typeScriptVersion": "5.1"}