const Discord = require("discord-user-bots");
const client = new Discord.Client();
import OpenAI from "openai";

const openai = new OpenAI({
        baseURL: 'https://api.deepseek.com',
        apiKey: process.env.DEEPSEEK_API_KEY,
});

client.on("ready", () => {
    console.log("Client online!");
});

client.on("message", async (message) => {
    console.log(message);

    if (message.author.id === "450330964481146880") {
        client.type(message.channel_id);
        await new Promise(resolve => setTimeout(resolve, 1000));
        client.send(
        message.channel_id,
        {
            content: "Hiii!"
        }
        );
        client.stop_type();
    }
});

client.login(process.env.USER_TOKEN);