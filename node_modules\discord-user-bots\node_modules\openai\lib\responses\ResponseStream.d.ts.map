{"version": 3, "file": "ResponseStream.d.ts", "sourceRoot": "", "sources": ["../../src/lib/responses/ResponseStream.ts"], "names": [], "mappings": "AAAA,OAAO,EACL,kBAAkB,EAClB,KAAK,cAAc,EAEnB,KAAK,wBAAwB,EAE7B,KAAK,mBAAmB,EACzB,MAAM,qCAAqC,CAAC;AAC7C,OAAO,KAAK,IAAI,MAAM,YAAY,CAAC;AAEnC,OAAO,MAAM,MAAM,aAAa,CAAC;AACjC,OAAO,EAAE,KAAK,UAAU,EAAE,WAAW,EAAE,MAAM,gBAAgB,CAAC;AAC9D,OAAO,EAAE,KAAK,uCAAuC,EAAE,KAAK,sBAAsB,EAAE,MAAM,cAAc,CAAC;AACzG,OAAO,EAAsB,oBAAoB,EAAE,MAAM,oBAAoB,CAAC;AAG9E,MAAM,MAAM,oBAAoB,GAAG,6BAA6B,GAAG,wBAAwB,CAAC;AAE5F,MAAM,MAAM,6BAA6B,GAAG,IAAI,CAAC,wBAAwB,EAAE,QAAQ,CAAC,GAAG;IACrF,MAAM,CAAC,EAAE,IAAI,CAAC;CACf,CAAC;AAEF,MAAM,MAAM,wBAAwB,GAAG;IACrC;;OAEG;IACH,WAAW,EAAE,MAAM,CAAC;IACpB;;OAEG;IACH,cAAc,CAAC,EAAE,MAAM,CAAC;IACxB;;;;;;OAMG;IACH,IAAI,CAAC,EAAE,kBAAkB,CAAC;IAE1B;;;OAGG;IACH,KAAK,CAAC,EAAE,oBAAoB,CAAC;CAC9B,CAAC;AAEF,KAAK,cAAc,GAAG,UAAU,GAC9B,IAAI,CACF;KACG,CAAC,IAAI,mBAAmB,CAAC,MAAM,CAAC,GAAG,CAAC,KAAK,EAAE,OAAO,CAAC,mBAAmB,EAAE;QAAE,IAAI,EAAE,CAAC,CAAA;KAAE,CAAC,KAAK,IAAI;CAC/F,EACD,4BAA4B,GAAG,wCAAwC,CACxE,GAAG;IACF,KAAK,EAAE,CAAC,KAAK,EAAE,mBAAmB,KAAK,IAAI,CAAC;IAC5C,4BAA4B,EAAE,CAAC,KAAK,EAAE,sBAAsB,KAAK,IAAI,CAAC;IACtE,wCAAwC,EAAE,CAAC,KAAK,EAAE,uCAAuC,KAAK,IAAI,CAAC;CACpG,CAAC;AAEJ,MAAM,MAAM,uBAAuB,GAAG,IAAI,CAAC,wBAAwB,EAAE,QAAQ,CAAC,GAAG;IAC/E,MAAM,CAAC,EAAE,IAAI,CAAC;CACf,CAAC;AAEF,qBAAa,cAAc,CAAC,OAAO,GAAG,IAAI,CACxC,SAAQ,WAAW,CAAC,cAAc,CAClC,YAAW,aAAa,CAAC,mBAAmB,CAAC;;gBAMjC,MAAM,EAAE,uBAAuB,GAAG,IAAI;IAKlD,MAAM,CAAC,cAAc,CAAC,OAAO,EAC3B,MAAM,EAAE,MAAM,EACd,MAAM,EAAE,oBAAoB,EAC5B,OAAO,CAAC,EAAE,IAAI,CAAC,cAAc,GAC5B,cAAc,CAAC,OAAO,CAAC;cAoFV,yBAAyB,CACvC,MAAM,EAAE,MAAM,EACd,MAAM,EAAE,oBAAoB,EAC5B,OAAO,CAAC,EAAE,IAAI,CAAC,cAAc,GAC5B,OAAO,CAAC,cAAc,CAAC,OAAO,CAAC,CAAC;IAiGnC,CAAC,MAAM,CAAC,aAAa,CAAC,CAAC,IAAI,EAAE,cAAc,CAAC,OAAO,CAAC,GAAG,aAAa,CAAC,mBAAmB,CAAC;IA6DzF;;;OAGG;IACG,aAAa,IAAI,OAAO,CAAC,cAAc,CAAC,OAAO,CAAC,CAAC;CAMxD"}