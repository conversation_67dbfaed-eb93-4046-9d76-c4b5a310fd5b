{"version": 3, "file": "parser.mjs", "sourceRoot": "", "sources": ["../src/lib/parser.ts"], "names": [], "mappings": "OAeO,EAAE,8BAA8B,EAAE,uBAAuB,EAAE,WAAW,EAAE;AAmB/E,MAAM,UAAU,2BAA2B,CACzC,eAAyC,EACzC,MAAoC;IAEpC,MAAM,GAAG,GAAG,EAAE,GAAG,eAAe,EAAE,CAAC;IAEnC,MAAM,CAAC,gBAAgB,CAAC,GAAG,EAAE;QAC3B,MAAM,EAAE;YACN,KAAK,EAAE,gCAAgC;YACvC,UAAU,EAAE,KAAK;SAClB;QACD,SAAS,EAAE;YACT,KAAK,EAAE,MAAM;YACb,UAAU,EAAE,KAAK;SAClB;KACF,CAAC,CAAC;IAEH,OAAO,GAA2C,CAAC;AACrD,CAAC;AASD,MAAM,UAAU,uBAAuB,CACrC,eAAmD,EACnD,MAAoC;IAEpC,MAAM,GAAG,GAAG,EAAE,GAAG,eAAe,EAAE,CAAC;IAEnC,MAAM,CAAC,gBAAgB,CAAC,GAAG,EAAE;QAC3B,MAAM,EAAE;YACN,KAAK,EAAE,gCAAgC;YACvC,UAAU,EAAE,KAAK;SAClB;QACD,SAAS,EAAE;YACT,KAAK,EAAE,MAAM;YACb,UAAU,EAAE,KAAK;SAClB;KACF,CAAC,CAAC;IAEH,OAAO,GAAuC,CAAC;AACjD,CAAC;AAED,MAAM,UAAU,4BAA4B,CAC1C,eAAoB;IAEpB,OAAO,eAAe,EAAE,CAAC,QAAQ,CAAC,KAAK,gCAAgC,CAAC;AAC1E,CAAC;AAqBD,MAAM,UAAU,iBAAiB,CAC/B,IAAwB,EACxB,EACE,MAAM,EACN,QAAQ,GAIT;IAED,MAAM,GAAG,GAAG,EAAE,GAAG,IAAI,EAAE,CAAC;IAExB,MAAM,CAAC,gBAAgB,CAAC,GAAG,EAAE;QAC3B,MAAM,EAAE;YACN,KAAK,EAAE,qBAAqB;YAC5B,UAAU,EAAE,KAAK;SAClB;QACD,SAAS,EAAE;YACT,KAAK,EAAE,MAAM;YACb,UAAU,EAAE,KAAK;SAClB;QACD,SAAS,EAAE;YACT,KAAK,EAAE,QAAQ;YACf,UAAU,EAAE,KAAK;SAClB;KACF,CAAC,CAAC;IAEH,OAAO,GAA+C,CAAC;AACzD,CAAC;AAED,MAAM,UAAU,kBAAkB,CAAC,IAAS;IAC1C,OAAO,IAAI,EAAE,CAAC,QAAQ,CAAC,KAAK,qBAAqB,CAAC;AACpD,CAAC;AAED,MAAM,UAAU,wBAAwB,CAGtC,UAA0B,EAAE,MAAc;IAC1C,IAAI,CAAC,MAAM,IAAI,CAAC,qBAAqB,CAAC,MAAM,CAAC,EAAE;QAC7C,OAAO;YACL,GAAG,UAAU;YACb,OAAO,EAAE,UAAU,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,MAAM,EAAE,EAAE,CAAC,CAAC;gBAC3C,GAAG,MAAM;gBACT,OAAO,EAAE;oBACP,GAAG,MAAM,CAAC,OAAO;oBACjB,MAAM,EAAE,IAAI;oBACZ,GAAG,CAAC,MAAM,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC;wBAC7B;4BACE,UAAU,EAAE,MAAM,CAAC,OAAO,CAAC,UAAU;yBACtC;wBACH,CAAC,CAAC,SAAS,CAAC;iBACb;aACF,CAAC,CAAC;SACJ,CAAC;KACH;IAED,OAAO,mBAAmB,CAAC,UAAU,EAAE,MAAM,CAAC,CAAC;AACjD,CAAC;AAED,MAAM,UAAU,mBAAmB,CAGjC,UAA0B,EAAE,MAAc;IAC1C,MAAM,OAAO,GAAiC,UAAU,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,MAAM,EAAyB,EAAE;QACrG,IAAI,MAAM,CAAC,aAAa,KAAK,QAAQ,EAAE;YACrC,MAAM,IAAI,uBAAuB,EAAE,CAAC;SACrC;QAED,IAAI,MAAM,CAAC,aAAa,KAAK,gBAAgB,EAAE;YAC7C,MAAM,IAAI,8BAA8B,EAAE,CAAC;SAC5C;QAED,OAAO;YACL,GAAG,MAAM;YACT,OAAO,EAAE;gBACP,GAAG,MAAM,CAAC,OAAO;gBACjB,GAAG,CAAC,MAAM,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC;oBAC7B;wBACE,UAAU,EACR,MAAM,CAAC,OAAO,CAAC,UAAU,EAAE,GAAG,CAAC,CAAC,QAAQ,EAAE,EAAE,CAAC,aAAa,CAAC,MAAM,EAAE,QAAQ,CAAC,CAAC,IAAI,SAAS;qBAC7F;oBACH,CAAC,CAAC,SAAS,CAAC;gBACZ,MAAM,EACJ,MAAM,CAAC,OAAO,CAAC,OAAO,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;oBACjD,mBAAmB,CAAC,MAAM,EAAE,MAAM,CAAC,OAAO,CAAC,OAAO,CAAC;oBACrD,CAAC,CAAC,IAAI;aACT;SACF,CAAC;IACJ,CAAC,CAAC,CAAC;IAEH,OAAO,EAAE,GAAG,UAAU,EAAE,OAAO,EAAE,CAAC;AACpC,CAAC;AAED,SAAS,mBAAmB,CAG1B,MAAc,EAAE,OAAe;IAC/B,IAAI,MAAM,CAAC,eAAe,EAAE,IAAI,KAAK,aAAa,EAAE;QAClD,OAAO,IAAI,CAAC;KACb;IAED,IAAI,MAAM,CAAC,eAAe,EAAE,IAAI,KAAK,aAAa,EAAE;QAClD,IAAI,WAAW,IAAI,MAAM,CAAC,eAAe,EAAE;YACzC,MAAM,eAAe,GAAG,MAAM,CAAC,eAAuD,CAAC;YAEvF,OAAO,eAAe,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC;SAC3C;QAED,OAAO,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;KAC5B;IAED,OAAO,IAAI,CAAC;AACd,CAAC;AAED,SAAS,aAAa,CACpB,MAAc,EACd,QAAuC;IAEvC,MAAM,SAAS,GAAG,MAAM,CAAC,KAAK,EAAE,IAAI,CAAC,CAAC,SAAS,EAAE,EAAE,CAAC,SAAS,CAAC,QAAQ,EAAE,IAAI,KAAK,QAAQ,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;IACzG,OAAO;QACL,GAAG,QAAQ;QACX,QAAQ,EAAE;YACR,GAAG,QAAQ,CAAC,QAAQ;YACpB,gBAAgB,EACd,kBAAkB,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,SAAS,CAAC,QAAQ,CAAC,QAAQ,CAAC,SAAS,CAAC;gBAChF,CAAC,CAAC,SAAS,EAAE,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,QAAQ,CAAC,SAAS,CAAC;oBACtE,CAAC,CAAC,IAAI;SACT;KACF,CAAC;AACJ,CAAC;AAED,MAAM,UAAU,mBAAmB,CACjC,MAAqD,EACrD,QAAuC;IAEvC,IAAI,CAAC,MAAM,EAAE;QACX,OAAO,KAAK,CAAC;KACd;IAED,MAAM,SAAS,GAAG,MAAM,CAAC,KAAK,EAAE,IAAI,CAAC,CAAC,SAAS,EAAE,EAAE,CAAC,SAAS,CAAC,QAAQ,EAAE,IAAI,KAAK,QAAQ,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;IACzG,OAAO,kBAAkB,CAAC,SAAS,CAAC,IAAI,SAAS,EAAE,QAAQ,CAAC,MAAM,IAAI,KAAK,CAAC;AAC9E,CAAC;AAED,MAAM,UAAU,qBAAqB,CAAC,MAAqC;IACzE,IAAI,4BAA4B,CAAC,MAAM,CAAC,eAAe,CAAC,EAAE;QACxD,OAAO,IAAI,CAAC;KACb;IAED,OAAO,CACL,MAAM,CAAC,KAAK,EAAE,IAAI,CAChB,CAAC,CAAC,EAAE,EAAE,CAAC,kBAAkB,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,KAAK,UAAU,IAAI,CAAC,CAAC,QAAQ,CAAC,MAAM,KAAK,IAAI,CAAC,CACtF,IAAI,KAAK,CACX,CAAC;AACJ,CAAC;AAED,MAAM,UAAU,kBAAkB,CAAC,KAAuC;IACxE,KAAK,MAAM,IAAI,IAAI,KAAK,IAAI,EAAE,EAAE;QAC9B,IAAI,IAAI,CAAC,IAAI,KAAK,UAAU,EAAE;YAC5B,MAAM,IAAI,WAAW,CACnB,2EAA2E,IAAI,CAAC,IAAI,IAAI,CACzF,CAAC;SACH;QAED,IAAI,IAAI,CAAC,QAAQ,CAAC,MAAM,KAAK,IAAI,EAAE;YACjC,MAAM,IAAI,WAAW,CACnB,SAAS,IAAI,CAAC,QAAQ,CAAC,IAAI,4FAA4F,CACxH,CAAC;SACH;KACF;AACH,CAAC"}