{"version": 3, "file": "ChatCompletionStream.d.ts", "sourceRoot": "", "sources": ["../src/lib/ChatCompletionStream.ts"], "names": [], "mappings": "AAAA,OAAO,KAAK,IAAI,MAAM,SAAS,CAAC;AAOhC,OAAO,EACL,0BAA0B,EAC1B,KAAK,cAAc,EACnB,KAAK,mBAAmB,EACxB,KAAK,0BAA0B,EAE/B,KAAK,8BAA8B,EACnC,KAAK,kBAAkB,EACxB,MAAM,2CAA2C,CAAC;AACnD,OAAO,EACL,4BAA4B,EAC5B,KAAK,kCAAkC,EACxC,MAAM,gCAAgC,CAAC;AACxC,OAAO,EAAE,KAAK,cAAc,EAAE,MAAM,iBAAiB,CAAC;AAEtD,OAAO,MAAM,MAAM,UAAU,CAAC;AAC9B,OAAO,EAAE,oBAAoB,EAAE,MAAM,oCAAoC,CAAC;AAW1E,MAAM,WAAW,iBAAiB;IAChC,KAAK,EAAE,MAAM,CAAC;IACd,QAAQ,EAAE,MAAM,CAAC;IACjB,MAAM,EAAE,OAAO,GAAG,IAAI,CAAC;CACxB;AAED,MAAM,WAAW,gBAAgB,CAAC,OAAO,GAAG,IAAI;IAC9C,OAAO,EAAE,MAAM,CAAC;IAChB,MAAM,EAAE,OAAO,GAAG,IAAI,CAAC;CACxB;AAED,MAAM,WAAW,iBAAiB;IAChC,KAAK,EAAE,MAAM,CAAC;IACd,QAAQ,EAAE,MAAM,CAAC;CAClB;AAED,MAAM,WAAW,gBAAgB;IAC/B,OAAO,EAAE,MAAM,CAAC;CACjB;AAED,MAAM,WAAW,mCAAmC;IAClD,IAAI,EAAE,MAAM,CAAC;IAEb,KAAK,EAAE,MAAM,CAAC;IAEd,SAAS,EAAE,MAAM,CAAC;IAElB,gBAAgB,EAAE,OAAO,CAAC;IAE1B,eAAe,EAAE,MAAM,CAAC;CACzB;AAED,MAAM,WAAW,kCAAkC;IACjD,IAAI,EAAE,MAAM,CAAC;IAEb,KAAK,EAAE,MAAM,CAAC;IAEd,SAAS,EAAE,MAAM,CAAC;IAElB,gBAAgB,EAAE,OAAO,CAAC;CAC3B;AAED,MAAM,WAAW,yBAAyB;IACxC,OAAO,EAAE,KAAK,CAAC,0BAA0B,CAAC,CAAC;IAC3C,QAAQ,EAAE,KAAK,CAAC,0BAA0B,CAAC,CAAC;CAC7C;AAED,MAAM,WAAW,wBAAwB;IACvC,OAAO,EAAE,KAAK,CAAC,0BAA0B,CAAC,CAAC;CAC5C;AAED,MAAM,WAAW,yBAAyB;IACxC,OAAO,EAAE,KAAK,CAAC,0BAA0B,CAAC,CAAC;IAC3C,QAAQ,EAAE,KAAK,CAAC,0BAA0B,CAAC,CAAC;CAC7C;AAED,MAAM,WAAW,wBAAwB;IACvC,OAAO,EAAE,KAAK,CAAC,0BAA0B,CAAC,CAAC;CAC5C;AAED,MAAM,WAAW,0BAA0B,CAAC,OAAO,GAAG,IAAI,CAAE,SAAQ,kCAAkC;IACpG,OAAO,EAAE,CAAC,YAAY,EAAE,MAAM,EAAE,eAAe,EAAE,MAAM,KAAK,IAAI,CAAC;IACjE,KAAK,EAAE,CAAC,KAAK,EAAE,mBAAmB,EAAE,QAAQ,EAAE,sBAAsB,KAAK,IAAI,CAAC;IAE9E,eAAe,EAAE,CAAC,KAAK,EAAE,iBAAiB,KAAK,IAAI,CAAC;IACpD,cAAc,EAAE,CAAC,KAAK,EAAE,gBAAgB,CAAC,OAAO,CAAC,KAAK,IAAI,CAAC;IAE3D,eAAe,EAAE,CAAC,KAAK,EAAE,iBAAiB,KAAK,IAAI,CAAC;IACpD,cAAc,EAAE,CAAC,KAAK,EAAE,gBAAgB,KAAK,IAAI,CAAC;IAElD,qCAAqC,EAAE,CAAC,KAAK,EAAE,mCAAmC,KAAK,IAAI,CAAC;IAC5F,oCAAoC,EAAE,CAAC,KAAK,EAAE,kCAAkC,KAAK,IAAI,CAAC;IAE1F,wBAAwB,EAAE,CAAC,KAAK,EAAE,yBAAyB,KAAK,IAAI,CAAC;IACrE,uBAAuB,EAAE,CAAC,KAAK,EAAE,wBAAwB,KAAK,IAAI,CAAC;IAEnE,wBAAwB,EAAE,CAAC,KAAK,EAAE,yBAAyB,KAAK,IAAI,CAAC;IACrE,uBAAuB,EAAE,CAAC,KAAK,EAAE,wBAAwB,KAAK,IAAI,CAAC;CACpE;AAED,MAAM,MAAM,0BAA0B,GAAG,IAAI,CAAC,8BAA8B,EAAE,QAAQ,CAAC,GAAG;IACxF,MAAM,CAAC,EAAE,IAAI,CAAC;CACf,CAAC;AAWF,qBAAa,oBAAoB,CAAC,OAAO,GAAG,IAAI,CAC9C,SAAQ,4BAA4B,CAAC,0BAA0B,CAAC,OAAO,CAAC,EAAE,OAAO,CACjF,YAAW,aAAa,CAAC,mBAAmB,CAAC;;gBAMjC,MAAM,EAAE,0BAA0B,GAAG,IAAI;IAMrD,IAAI,6BAA6B,IAAI,sBAAsB,GAAG,SAAS,CAEtE;IAED;;;;;;OAMG;IACH,MAAM,CAAC,kBAAkB,CAAC,MAAM,EAAE,cAAc,GAAG,oBAAoB,CAAC,IAAI,CAAC;IAM7E,MAAM,CAAC,oBAAoB,CAAC,OAAO,EACjC,MAAM,EAAE,MAAM,EACd,MAAM,EAAE,0BAA0B,EAClC,OAAO,CAAC,EAAE,IAAI,CAAC,cAAc,GAC5B,oBAAoB,CAAC,OAAO,CAAC;cA8MP,qBAAqB,CAC5C,MAAM,EAAE,MAAM,EACd,MAAM,EAAE,0BAA0B,EAClC,OAAO,CAAC,EAAE,IAAI,CAAC,cAAc,GAC5B,OAAO,CAAC,oBAAoB,CAAC,OAAO,CAAC,CAAC;cAuBzB,mBAAmB,CACjC,cAAc,EAAE,cAAc,EAC9B,OAAO,CAAC,EAAE,IAAI,CAAC,cAAc,GAC5B,OAAO,CAAC,cAAc,CAAC;IA8I1B,CAAC,MAAM,CAAC,aAAa,CAAC,CAAC,IAAI,EAAE,oBAAoB,CAAC,OAAO,CAAC,GAAG,aAAa,CAAC,mBAAmB,CAAC;IA6D/F,gBAAgB,IAAI,cAAc;CAInC;AAwGD;;;GAGG;AACH,MAAM,WAAW,sBAAsB;IACrC;;OAEG;IACH,EAAE,EAAE,MAAM,CAAC;IAEX;;;OAGG;IACH,OAAO,EAAE,KAAK,CAAC,sBAAsB,CAAC,MAAM,CAAC,CAAC;IAE9C;;OAEG;IACH,OAAO,EAAE,MAAM,CAAC;IAEhB;;OAEG;IACH,KAAK,EAAE,MAAM,CAAC;IAMd;;;;;OAKG;IACH,kBAAkB,CAAC,EAAE,MAAM,CAAC;CAC7B;AAED,yBAAiB,sBAAsB,CAAC;IACtC,UAAiB,MAAM;QACrB;;WAEG;QACH,OAAO,EAAE,MAAM,CAAC,OAAO,CAAC;QAExB;;;;;;WAMG;QACH,aAAa,EAAE,cAAc,CAAC,MAAM,CAAC,eAAe,CAAC,GAAG,IAAI,CAAC;QAE7D;;WAEG;QACH,QAAQ,EAAE,cAAc,CAAC,MAAM,CAAC,QAAQ,GAAG,IAAI,CAAC;QAEhD;;WAEG;QACH,KAAK,EAAE,MAAM,CAAC;KACf;IAED,UAAiB,MAAM,CAAC;QACtB;;WAEG;QACH,UAAiB,OAAO;YACtB;;eAEG;YACH,OAAO,CAAC,EAAE,MAAM,GAAG,IAAI,CAAC;YAExB,OAAO,CAAC,EAAE,MAAM,GAAG,IAAI,CAAC;YAExB,MAAM,CAAC,EAAE,OAAO,GAAG,IAAI,CAAC;YAExB;;;eAGG;YACH,aAAa,CAAC,EAAE,OAAO,CAAC,YAAY,CAAC;YAErC,UAAU,CAAC,EAAE,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;YAErC;;eAEG;YACH,IAAI,CAAC,EAAE,kBAAkB,CAAC;SAC3B;QAED,UAAiB,OAAO,CAAC;YACvB,UAAiB,QAAQ;gBACvB;;mBAEG;gBACH,EAAE,EAAE,MAAM,CAAC;gBAEX,QAAQ,EAAE,QAAQ,CAAC,QAAQ,CAAC;gBAE5B;;mBAEG;gBACH,IAAI,EAAE,UAAU,CAAC;aAClB;YAED,UAAiB,QAAQ,CAAC;gBACxB,UAAiB,QAAQ;oBACvB;;;;;uBAKG;oBACH,SAAS,EAAE,MAAM,CAAC;oBAElB,gBAAgB,CAAC,EAAE,OAAO,CAAC;oBAE3B;;uBAEG;oBACH,IAAI,EAAE,MAAM,CAAC;iBACd;aACF;YAED;;;eAGG;YACH,UAAiB,YAAY;gBAC3B;;;;;mBAKG;gBACH,SAAS,CAAC,EAAE,MAAM,CAAC;gBAEnB;;mBAEG;gBACH,IAAI,CAAC,EAAE,MAAM,CAAC;aACf;SACF;KACF;CACF"}